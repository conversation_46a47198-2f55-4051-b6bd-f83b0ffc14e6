<wxs src="../wxs/utils.wxs" module="utils" />

<view class="van-checkbox custom-class">
  <view class="van-checkbox__icon-wrap" bindtap="toggle">
    <slot wx:if="{{ useIconSlot }}" name="icon" />
    <van-icon
      wx:else
      name="success"
      class="{{ utils.bem('checkbox__icon', [shape, { disabled, checked: value }]) }}"
      style="{{ checkedColor && value && !disabled ? 'border-color:' + checkedColor + '; background-color:' + checkedColor : '' }}"
      custom-class="icon-class"
      custom-style="line-height: 20px;"
    />
  </view>
  <view class="label-class {{ utils.bem('checkbox__label', [labelPosition, { disabled }]) }}" bindtap="onClickLabel">
    <slot />
  </view>
</view>
