# 修复 vant-weapp 组件路径问题

## 问题描述

微信小程序编译时出现组件路径错误：

```
pages/login/index.json: ["usingComponents"]["van-button"]: "miniprogram_npm/vant-weapp/button/index"，在多个路径下未找到组件
```

## 问题原因

在 `pages/login/index.json` 中，vant-weapp 组件的路径配置使用了相对路径：
```json
{
  "usingComponents": {
    "van-button": "miniprogram_npm/vant-weapp/button/index"
  }
}
```

微信小程序编译器会从当前页面目录开始查找相对路径，导致路径解析错误。

## 解决方案

将组件路径改为绝对路径（以 `/` 开头）：
```json
{
  "usingComponents": {
    "van-button": "/miniprogram_npm/vant-weapp/button/index"
  }
}
```

## 修复过程

1. **分析问题**：检查项目结构，确认 vant-weapp 组件库存在于 `miniprogram/miniprogram_npm/vant-weapp/` 目录
2. **定位错误**：确认只有 `pages/login/index.json` 存在此问题
3. **修复路径**：将相对路径改为绝对路径
4. **验证修复**：确认其他页面无类似问题

## 修复结果

- ✅ 修复了 `pages/login/index.json` 中的组件路径
- ✅ 确认其他页面无类似问题
- ✅ 组件路径现在使用正确的绝对路径格式

## 预防措施

今后在引用 npm 包中的组件时，建议：
1. 使用绝对路径（以 `/` 开头）
2. 参考 vant-weapp 官方文档的路径配置示例
3. 在添加新组件时进行编译测试

## 相关文件

- `miniprogram/pages/login/index.json` - 已修复
