<wxs src="../wxs/utils.wxs" module="utils" />

<view
  class="custom-class {{ utils.bem('cell', [size, { center, required, borderless: !border, clickable: isLink || clickable }]) }}"
  hover-class="van-cell--hover hover-class"
  hover-stay-time="70"
  style="{{ customStyle }}"
  bind:tap="onClick"
>
  <van-icon
    wx:if="{{ icon }}"
    name="{{ icon }}"
    class="van-cell__left-icon-wrap"
    custom-class="van-cell__left-icon"
  />
  <slot wx:else name="icon" />

  <view
    style="{{ titleWidth ? 'max-width:' + titleWidth + ';min-width:' + titleWidth : '' }}"
    class="van-cell__title title-class"
  >
    <block wx:if="{{ title }}">{{ title }}</block>
    <slot wx:else name="title" />

    <view wx:if="{{ label || useLabelSlot }}" class="van-cell__label label-class">
      <slot wx:if="{{ useLabelSlot }}" name="label" />
      <block wx:elif="{{ label }}">{{ label }}</block>
    </view>
  </view>

  <view class="van-cell__value value-class">
    <block wx:if="{{ value || value === 0 }}">{{ value }}</block>
    <slot wx:else />
  </view>

  <van-icon
    wx:if="{{ isLink }}"
    name="{{ arrowDirection ? 'arrow' + '-' + arrowDirection : 'arrow' }}"
    class="van-cell__right-icon-wrap right-icon-class"
    custom-class="van-cell__right-icon"
  />
  <slot wx:else name="right-icon" />

  <slot name="extra" />
</view>
