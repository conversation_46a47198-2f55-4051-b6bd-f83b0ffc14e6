<wxs src="../wxs/utils.wxs" module="utils" />

<view
  class="custom-class {{ classPrefix }} {{ utils.isSrc(name) ? 'van-icon--image' : classPrefix + '-' + name }}"
  style="{{ color ? 'color: ' + color + ';' : '' }}{{ size ? 'font-size: ' + size + ';' : '' }}{{ customStyle }}"
  bind:tap="onClick"
>
  <van-info
    wx:if="{{ info !== null }}"
    info="{{ info }}"
    custom-class="van-icon__info"
  />
  <image
    wx:if="{{ utils.isSrc(name) }}"
    src="{{ name }}"
    mode="aspectFit"
    class="van-icon__image"
  />
</view>
