/* 登录页面样式 */
.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 48rpx 32rpx;
  box-sizing: border-box;
  background: #fff;
}

.logo-section {
  margin-top: 120rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.logo-img {
  width: 180rpx;
  height: 180rpx;
  margin-bottom: 24rpx;
}

.logo-text {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
}

.login-content {
  width: 100%;
  margin-top: 120rpx;
}

.login-section {
  width: 100%;
}

/* 自定义按钮样式 */
.login-btn {
  width: 100% !important;
  height: 88rpx !important;
  line-height: 88rpx !important;
  text-align: center !important;
  background: #07c160 !important;
  color: #fff !important;
  font-size: 32rpx !important;
  border-radius: 44rpx !important;
  margin-bottom: 32rpx !important;
  padding: 0 !important;
  border: none !important;
}

.agreement-tips {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  line-height: 1.4;
}

.tips-text {
  color: #999;
}

.link {
  color: #07c160;
  display: inline;
}

/* 手机号绑定弹窗样式 */
.phone-popup {
  width: 560rpx !important;
}

.popup-content {
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
}

.popup-header {
  position: relative;
  padding: 32rpx;
  text-align: center;
  border-bottom: 1rpx solid #eee;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-top: 16rpx;
}

.popup-body {
  padding: 48rpx 32rpx;
  text-align: center;
}

.popup-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 48rpx;
  line-height: 1.5;
  display: block;
}

/* 自定义手机号授权按钮样式 */
.phone-auth-btn {
  width: 100% !important;
  height: 80rpx !important;
  line-height: 80rpx !important;
  text-align: center !important;
  background: #07c160 !important;
  color: #fff !important;
  font-size: 28rpx !important;
  border-radius: 40rpx !important;
  padding: 0 !important;
  border: none !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}