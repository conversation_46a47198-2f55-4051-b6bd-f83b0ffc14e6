{"name": "<PERSON><PERSON>-we<PERSON><PERSON>", "version": "0.5.29", "author": "you<PERSON>", "license": "MIT", "miniprogram": "dist", "description": "轻量、可靠的小程序 UI 组件库", "scripts": {"dev": "node build/dev.js", "lint": "eslint ./packages --ext .js,.ts", "release": "sh build/release.sh", "release:site": "sh build/release-site.sh", "build:lib": "yarn && npx gulp -f build/compiler.js --series buildEs buildLib", "build:changelog": "vant changelog --tag v0.5.0 ./docs/markdown/changelog.generated.md"}, "files": ["dist", "lib"], "repository": {"type": "git", "url": "git+ssh://**************/youzan/vant-weapp.git"}, "husky": {"hooks": {"commit-msg": "vant commit-lint"}}, "homepage": "https://github.com/youzan/vant-weapp#readme", "devDependencies": {"@babel/core": "^7.5.5", "@babel/plugin-syntax-dynamic-import": "^7.2.0", "@babel/preset-env": "^7.5.5", "@vant/cli": "^1.0.3", "@vant/doc": "^2.5.1", "@vant/eslint-config": "^1.2.4", "@vant/icons": "1.2.0", "@vant/markdown-loader": "^2.2.0", "autoprefixer": "^9.6.1", "babel-loader": "8.0.6", "cross-env": "^5.1.4", "css-loader": "^3.2.0", "cssnano": "^4.1.10", "eslint": "^6.1.0", "gh-pages": "^2.0.1", "gulp": "^4.0.2", "gulp-insert": "^0.5.0", "gulp-less": "^4.0.1", "gulp-postcss": "^8.0.0", "gulp-rename": "^1.2.2", "html-webpack-plugin": "^3.2.0", "less": "^3.9.0", "less-loader": "^5.0.0", "miniprogram-api-typings": "2.7.7-2", "postcss-loader": "^3.0.0", "progress-bar-webpack-plugin": "^1.11.0", "style-loader": "^1.0.0", "typescript": "^3.5.3", "vue": "2.6.10", "vue-loader": "^15.7.1", "vue-router": "^3.1.1", "vue-template-compiler": "2.6.10", "webpack": "^4.39.1", "webpack-cli": "^3.3.6", "webpack-serve": "^2.0.3"}}