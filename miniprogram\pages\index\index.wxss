/**index.wxss**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f7f8fa;
}

.scrollarea {
  flex: 1;
  overflow-y: auto;
}

.container {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

/* 社区头部样式 */
.community-header {
  position: relative;
  height: 320rpx;
  width: 100%;
  overflow: hidden;
  flex-shrink: 0;
}

.community-banner {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.community-info {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 24rpx;
  background: linear-gradient(to top, rgba(0,0,0,0.6), transparent);
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.community-name {
  font-size: 40rpx;
  color: #fff;
  font-weight: 600;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
}

.weather-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  color: #fff;
  font-size: 28rpx;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
}

/* 用户卡片样式 */
.user-card-container {
  margin: 24rpx;
}

.user-card {
  border-radius: 16rpx !important;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06) !important;
}

.user-avatar {
  width: 120rpx !important;
  height: 120rpx !important;
  border-radius: 60rpx !important;
  border: 4rpx solid rgba(7,193,96,0.1) !important;
  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.05) !important;
}

.user-tags {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex-wrap: wrap;
  margin-top: 8rpx;
}

.phone-text {
  font-size: 28rpx;
  color: #666;
}

/* 功能区域样式 */
.function-grid-container {
  margin: 0rpx 24rpx;
  padding: 20rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);
}

.function-grid {
  width: 100% !important;
}

.grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24rpx 0;
  border-radius: 12rpx;
  transition: all 0.3s;
  text-align: center;
}

.grid-item:active {
  background: #f8f8f8;
  transform: scale(0.98);
}

.grid-item text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-top: 16rpx;
}

/* 社区动态样式 */
.community-news-container {
  margin: 24rpx;
}

.community-news {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.06);
  padding: 24rpx;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
  padding-bottom: 10rpx;
}

.section-title text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.section-title .more {
  font-size: 26rpx;
  color: #07c160;
  display: flex;
  align-items: center;
}

.news-list {
  border-radius: 0 !important;
  box-shadow: none !important;
  background: transparent !important;
}

.news-item {
  border-bottom: 1rpx solid #f5f5f5 !important;
  background: transparent !important;
}

.news-item:last-child {
  border-bottom: none !important;
}

.news-icon {
  width: 88rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
  background: rgba(7,193,96,0.1);
  margin-right: 16rpx;
}

.news-tag {
  margin-left: 16rpx;
}

/* 登录区域样式 */
.login-section {
  margin: 24rpx;
  height: 280rpx;
  position: relative;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.1);
  flex-shrink: 0;
}

.login-section.show {
  display: block;  /* 需要显示时添加show类 */
}

.login-bg {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.login-content {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: rgba(0,0,0,0.4);
  padding: 40rpx;
}

.login-title {
  font-size: 36rpx;
  color: #fff;
  font-weight: 600;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
}

.login-desc {
  font-size: 28rpx;
  color: rgba(255,255,255,0.9);
  margin-bottom: 40rpx;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
}

.login-btn {
  background: #07c160 !important;
  color: #fff !important;
  font-size: 24rpx;
  padding: 0 60rpx;
  height: 88rpx;
  line-height: 56rpx;
  border-radius: 44rpx;
  font-weight: 500;
  border: none;
}

.login-btn::after {
  border: none;
}

