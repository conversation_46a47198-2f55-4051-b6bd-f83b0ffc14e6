<view class="container">
  <!-- Logo 区域 -->
  <view class="logo-section">
    <image class="logo-img" src="/static/images/logo.png" mode="aspectFit"></image>
    <text class="logo-text">欢迎登录智慧小区</text>
  </view>

  <!-- 登录内容区域 -->
  <view class="login-content">
    <view class="login-section">
      <van-button
        type="primary"
        size="large"
        round
        block
        bindtap="handleLogin"
        custom-class="login-btn"
      >
        手机号快捷登录
      </van-button>

      <view class="agreement-tips">
        <text class="tips-text">登录即表示同意</text>
        <text class="link" bindtap="goToAgreement">《用户协议》</text>
        <text class="tips-text">和</text>
        <text class="link" bindtap="goToPrivacy">《隐私政策》</text>
      </view>
    </view>
  </view>

  <!-- 手机号绑定弹窗 -->
  <van-popup
    show="{{ showPhoneModal }}"
    position="center"
    round
    closeable
    bind:close="closePhoneModal"
    custom-class="phone-popup"
  >
    <view class="popup-content">
      <view class="popup-header">
        <van-icon name="phone-o" size="48rpx" color="#07c160" />
        <text class="popup-title">绑定手机号</text>
      </view>

      <view class="popup-body">
        <text class="popup-desc">为了给您提供更好的服务，请绑定手机号</text>

        <van-button
          type="primary"
          size="large"
          round
          block
          open-type="getPhoneNumber"
          bindgetphonenumber="getPhoneNumber"
          custom-class="phone-auth-btn"
        >
          <van-icon name="phone-o" size="32rpx" />
          <text style="margin-left: 16rpx;">授权手机号</text>
        </van-button>
      </view>
    </view>
  </van-popup>
</view>