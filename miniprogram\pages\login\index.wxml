<view class="container">
  <view class="logo">
    <image class="logo-img" src="/static/images/logo.png" mode="aspectFit"></image>
    <text class="logo-text">欢迎登录智慧小区</text>
  </view>

  <view class="content">
    <view class="login-section">
      <van-button class="login-btn" type="primary" block bindtap="handleLogin">手机号快捷登录</van-button>
      <view class="tips">登录即表示同意<text class="link" bindtap="goToAgreement">《用户协议》</text>和<text class="link" bindtap="goToPrivacy">《隐私政策》</text></view>
    </view>
  </view>

  <!-- 手机号绑定弹窗 -->
  <view class="phone-modal" wx:if="{{showPhoneModal}}">
    <view class="modal-mask" bindtap="closePhoneModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">绑定手机号</text>
        <text class="modal-close" bindtap="closePhoneModal">×</text>
      </view>
      <view class="modal-body">
        <view class="modal-text">为了给您提供更好的服务，请绑定手机号</view>
        <button class="modal-phone-btn" open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber">
          授权手机号
        </button>
      </view>
    </view>
  </view>
</view> 