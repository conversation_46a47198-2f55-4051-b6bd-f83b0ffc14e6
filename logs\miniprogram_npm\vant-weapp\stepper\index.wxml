<wxs src="../wxs/utils.wxs" module="utils" />

<view class="van-stepper custom-class">
  <view
    class="minus-class {{ utils.bem('stepper__minus', { disabled: minusDisabled }) }}"
    style="{{ showMinus ? '' : 'display: none;' }}"
    hover-class="van-stepper__minus--hover"
    hover-stay-time="70"
    bind:tap="onMinus"
  />
  <input
    type="{{ integer ? 'number' : 'digit' }}"
    class="input-class {{ utils.bem('stepper__input', { disabled: disabled || disableInput }) }}"
    style="{{ inputWidth ? 'width: ' + inputWidth : '' }}"
    value="{{ value }}"
    focus="{{ focus }}"
    disabled="{{ disabled || disableInput }}"
    bindinput="onInput"
    bind:focus="onFocus"
    bind:blur="onBlur"
  />
  <view
    class="plus-class {{ utils.bem('stepper__plus', { disabled: plusDisabled }) }}"
    style="{{ showPlus ? '' : 'display: none;' }}"
    hover-class="van-stepper__plus--hover"
    hover-stay-time="70"
    bind:tap="onPlus"
  />
</view>
