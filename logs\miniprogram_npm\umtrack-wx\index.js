"use strict";var e="[UMENG] -- ",t=function(){var t=null,n=!1;function i(){this.setDebug=function(e){n=e};this.d=function(){if(n)try{"string"==typeof arguments[0]&&(arguments[0]=e+arguments[0]);console.debug.apply(console,arguments)}catch(e){}};this.i=function(){try{if(n)try{"string"==typeof arguments[0]&&(arguments[0]=e+arguments[0]);console.info.apply(console,arguments)}catch(e){}}catch(e){}};this.e=function(){if(n)try{"string"==typeof arguments[0]&&(arguments[0]=e+arguments[0]);console.error.apply(console,arguments)}catch(e){}};this.w=function(){if(n)try{"string"==typeof arguments[0]&&(arguments[0]=e+arguments[0]);console.warn.apply(console,arguments)}catch(e){}};this.v=function(){if(n)try{"string"==typeof arguments[0]&&(arguments[0]=e+arguments[0]);console.log.apply(console,arguments)}catch(e){}};this.t=function(){if(n)try{console.table.apply(console,arguments)}catch(e){}};this.tip=function(){try{"string"==typeof arguments[0]&&(arguments[0]=e+arguments[0]);console.log.apply(console,arguments)}catch(e){}};this.tip_w=function(e){try{console.log("%c [UMENG] -- "+e,"background:red; padding: 4px; padding-right: 8px; border-radius: 4px; color: #fff;")}catch(e){}};this.err=function(){try{"string"==typeof arguments[0]&&(arguments[0]=e+arguments[0]);console.error.apply(console,arguments)}catch(e){}};this.repeat=function(e){for(var t=e;t.length<86;)t+=e;return t}}return function(){null===t&&(t=new i);return t}}(),n=function(){var e=null;function t(){var e={};this.useOpenid=function(){return!!e.useOpenid};this.useSwanid=function(){return!!e.useSwanid};this.autoGetOpenid=function(){return!!e.autoGetOpenid};this.appKey=function(){return e.appKey};this.uploadUserInfo=function(){return e.uploadUserInfo};this.enableVerify=function(){return e.enableVerify};this.set=function(t){e=t};this.get=function(){return e};this.setItem=function(t,n){e[t]=n};this.getItem=function(t){return e[t]}}return function(){e||(e=new t);return e}}();function i(){}i.prototype={on:function(e,t,n){var i=this.e||(this.e={});(i[e]||(i[e]=[])).push({fn:t,ctx:n});return this},once:function(e,t,n){var i=this;function r(){i.off(e,r);t.apply(n,arguments)}r._=t;return this.on(e,r,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),i=0,r=n.length;i<r;i++)n[i].fn.apply(n[i].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),i=n[e],r=[];if(i&&t)for(var o=0,s=i.length;o<s;o++)i[o].fn!==t&&i[o].fn._!==t&&r.push(i[o]);r.length?n[e]=r:delete n[e];return this}};var r=new i;r.messageType={CONFIG_LOADED:0,UMA_LIB_INITED:1};var o=new(function(){function e(){}e.prototype.setStorage=function(e,t,n){wx.setStorage({key:e,data:t,success:function(){"function"==typeof n&&n(!0)},fail:function(){"function"==typeof n&&n(!1)}})};e.prototype.getStorage=function(e,n){wx.getStorage({key:e,success:function(e){"function"==typeof n&&n(e.data)},fail:function(i){t().w(e+": "+i.errMsg);"function"==typeof n&&n()}})};e.prototype.removeStorage=function(e,t){wx.removeStorage({key:e,success:function(){"function"==typeof t&&t(!0)},fail:function(){"function"==typeof t&&t(!1)}})};e.prototype.getSystemInfo=function(e){wx.getSystemInfo({success:function(t){t.safeArea=t.safeArea||{};var n="";t.host&&"string"==typeof t.host.env&&(n=t.host.env);var i={model:t.model,brand:t.brand,pixelRatio:t.pixelRatio,screenWidth:t.screenWidth,screenHeight:t.screenHeight,fontSizeSetting:t.fontSizeSetting,platform:t.platform,platformVersion:t.version,platformSDKVersion:t.SDKVersion,language:t.language,deviceName:t.model,OSVersion:t.system,resolution:"",theme:t.theme,benchmarkLevel:t.benchmarkLevel,safeArea:{width:t.safeArea.width,height:t.safeArea.height,top:t.safeArea.top,left:t.safeArea.left,bottom:t.safeArea.bottom,right:t.safeArea.right},statusBarHeight:t.statusBarHeight,host:n},r=t.system.split(" ");Array.isArray(r)&&(i.OS=r[0]);var o=Math.round(t.screenWidth*t.pixelRatio),s=Math.round(t.screenHeight*t.pixelRatio);i.resolution=o>s?o+"*"+s:s+"*"+o;"function"==typeof e&&e(i)},fail:function(){"function"==typeof e&&e()}})};e.prototype.getDeviceInfo=function(e){"function"==typeof e&&e("")};e.prototype.checkNetworkAvailable=function(e){wx.getNetworkType({success:function(t){"function"==typeof e&&e(t&&"none"!==t.networkType)},fail:function(){"function"==typeof e&&e(!1)}})};e.prototype.getNetworkInfo=function(e){wx.getNetworkType({success:function(t){"function"==typeof e&&e({networkAvailable:"none"!==t.networkType,networkType:t.networkType})},fail:function(){"function"==typeof e&&e()}})};e.prototype.getDeviceId=function(e){e("")};e.prototype.getAdvertisingId=function(e){"function"==typeof e&&e("")};e.prototype.onNetworkStatusChange=function(e){wx.onNetworkStatusChange((function(t){"function"==typeof e&&e(t.isConnected)}))};e.prototype.request=function(e){var t=e.success,n=e.fail,i=!1,r=null;e.success=function(e){if(!i){r&&clearTimeout(r);"function"==typeof t&&t(e)}};e.fail=function(){if(!i){r&&clearTimeout(r);"function"==typeof n&&n(!1)}};wx.request(e);r=setTimeout((function(){r&&clearTimeout(r);i=!0;"function"==typeof n&&n(i)}),e.timeout||5e3)};e.prototype.getSdkType=function(){return"wxmp"};e.prototype.getPlatform=function(){return"wx"};e.prototype.getUserInfo=function(e){e()};e.prototype.getAppInfoSync=function(){if(wx.getAccountInfoSync){var e=wx.getAccountInfoSync(),t=e&&e.miniProgram?e.miniProgram:{};return{appId:t.appId,appEnv:t.envVersion,appVersion:t.version}}return{}};e.prototype.onShareAppMessage=function(e){wx.onShareAppMessage(e)};e.prototype.shareAppMessage=function(e){wx.shareAppMessage(e)};e.prototype.getLaunchOptionsSync=function(){var e=null;if(e)return e;if(!wx.getLaunchOptionsSync)return{};try{e=wx.getLaunchOptionsSync()}catch(t){e=null}return e||{}};return e}()),s=function(e,t){s=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])};return s(e,t)};function a(e,t){s(e,t);function n(){this.constructor=e}e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var u={SESSION_INTERVAL:3e4,LOG_URL:"/wxm_logs",GET_OPENID_URL:"/uminiprogram_logs/wx/getuut",USERINFO_URL:"/uminiprogram_logs/comm/uif",ENDPOINT:"https://umini.shujupie.com",ENDPOINTB:"https://ulogs.umeng.com",DEVICE_INFO_KEY:"device_info",ADVERTISING_ID:"mobile_ad_id",ANDROID_ID:"android_id",CURRENT_SESSION:"current_session",SESSION_PAUSE_TIME:"session_pause_time",EVENT_SEND_DEFAULT_INTERVAL:15e3,EVENT_LAST_SEND_TIME:"last_send_time",MAX_EVENTID_LENGTH:128,MAX_PROPERTY_KEY_LENGTH:256,MAX_PROPERTY_KEYS_COUNT:100,REPORT_POLICY:"report_policy",REPORT_INTERVAL_TIME:"report_interval_time",REPORT_POLICY_START_SEND:"1",REPORT_POLICY_INTERVAL:"6",IMPRINT:"imprint",SEED_VERSION:"1.0.0",IMPL_VERSION:"2.8.0",ALIPAY_AVAILABLE_VERSION:"10.1.52",SHARE_PATH:"um_share_path",SHARES:"shares",REQUESTS:"requests",UUID:"um_uuid",UUID_SUFFIX:"ud",OPENID:"um_od",UNIONID:"um_unid",ALIPAYID:"um_alipayid",USERID:"um_userid",PROVIDER:"um_provider",SWANID:"um_swanid",ANONYMOUSID:"um_anonymousid",LAUNCH_OPTIONS:"LAUNCH_OPTIONS",UM_SSRC:"_um_ssrc",USER_INFO:"user_info",IS_ALIYUN:!1};var c,f={isNumber:function(e){return!Number.isNaN(parseInt(e,10))},compareVersion:function(e,t){for(var n=String(e).split("."),i=String(t).split("."),r=0;r<Math.max(n.length,i.length);r++){var o=parseInt(n[r]||0,10),s=parseInt(i[r]||0,10);if(o>s)return 1;if(o<s)return-1}return 0},getRandomStr:function(e){for(var t="",n=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"],i=0;i<Number(e);i++){t+=n[Math.round(Math.random()*(n.length-1))]}return t},clone:function(e){return JSON.parse(JSON.stringify(e))},startsWith:function(e,t){return!(!e||!t||0===t.length||t.length>e.length)&&e.substr(0,t.length)===t},endsWith:function(e,t){return!(!t||0===e.length||t.length>e.length)&&e.substring(e.length-t.length)===t},assign:function(e){if(null==e)throw new TypeError("Cannot convert undefined or null to object");for(var t=Object(e),n=1;n<arguments.length;n++){var i=arguments[n];if(i)for(var r in i)Object.prototype.hasOwnProperty.call(i,r)&&(t[r]=i[r])}return t},deepEqual:function e(t,n){if(t===n)return!0;if(t&&"object"==typeof t&&n&&"object"==typeof n){if(Object.keys(t).length!==Object.keys(n).length)return!1;for(var i in t){if(Object.prototype.hasOwnProperty.call(n,i))return!1;if(!e(t[i],n[i]))return!1}return!0}return!1},trimStart:function(e,t){if(!e)return"";if("string"==typeof t&&t.length){var n=new RegExp("^"+t+"*");e=e.replace(n,"")}else e=e.replace(/^s*/,"");return e},trimEnd:function(e,t){if(!e)return"";var n,i;if("string"==typeof t&&t.length){n=new RegExp(t);i=e.length;for(;n.test(e.charAt(i));)i-=1;return e.slice(0,i+1)}n=/s/;i=e.length-1;for(;n.test(e.charAt(i));)i-=1;return e.slice(0,i+1)},isFunction:function(e){return"function"==typeof e}},p=function(e){a(n,e);function n(){return null!==e&&e.apply(this,arguments)||this}n.prototype.getOpenIdAsync=function(e,n){var i=this;wx.login({success:function(r){r.code?o.request({url:u.ENDPOINT+u.GET_OPENID_URL,method:"GET",data:{key:e,code:r.code},success:function(e){if(e&&200===e.statusCode&&e.data&&e.data.data){var t=e.data.data;i.setOpenid(t.oid);i.setUnionid(t.uid);return n&&n(!0)}n&&n()},fail:function(e){t().v("wx request failed...",e);n&&n()}}):n&&n()},fail:function(){n&&n()}})};return n}(function(e){a(n,e);function n(){var t=null!==e&&e.apply(this,arguments)||this;t._openid="";t._unionid="";t._useOpenid=!1;return t}n.prototype.initID=function(e){var n=this;n._idType=n._useOpenid?"openid":"uuid";t().v("id type: ",n._idType);o.getStorage(u.UNIONID,(function(e){n._unionid=e}));this._useOpenid?o.getStorage(u.OPENID,(function(t){n._openid=t;e&&e()})):e&&e()};n.prototype.setUseOpenid=function(e){this._useOpenid=e};n.prototype.setOpenid=function(e){if(!this._openid&&e){this._openid=e;o.setStorage(u.OPENID,e)}};n.prototype.setUnionid=function(e){if(!this._unionid&&e){this._unionid=e;o.setStorage(u.UNIONID,e)}};n.prototype.getIdTracking=function(){var t=e.prototype.getIdTracking.call(this);this._openid&&(t.openid=this._openid);this._unionid&&(t.unionid=this._unionid);this._userid&&(t.userid=this._userid);return t};n.prototype.getId=function(){return this._useOpenid?this._openid:this._uuid};return n}(function(){function e(){this._uuid="";this._userid="";this._provider="";this._idType=""}e.prototype.createUUID=function(){return f.getRandomStr(10)+Date.now()+f.getRandomStr(7)+u.UUID_SUFFIX};e.prototype.initUUID=function(e){var t=this;o.getStorage(u.UUID,(function(n){if(n)t._uuid=n;else{t._uuid=t.createUUID();o.setStorage(u.UUID,t._uuid)}e&&e(n)}))};e.prototype.initUserid=function(){var e=this;o.getStorage(u.USERID,(function(n){if(!e._userid&&n){e._userid=n;t().v("userId is ",n)}}));o.getStorage(u.PROVIDER,(function(n){if(!e._provider&&n){e._provider=n;t().v("provider is ",n)}}))};e.prototype.init=function(e){var t=this;t.initUUID((function(){t.initUserid();t.initID(e)}))};e.prototype.setUserid=function(e,t){if(!this._userid&&e){this._userid=e;this._provider=t;o.setStorage(u.USERID,e);o.setStorage(u.PROVIDER,t)}};e.prototype.removeUserid=function(){this._userid=void 0;this._provider=void 0;o.removeStorageSync(u.USERID);o.removeStorageSync(u.PROVIDER)};e.prototype.getUserId=function(){return this._userid};e.prototype.getProvider=function(){return this._provider};e.prototype.getIdType=function(){return this._idType};e.prototype.getIdTracking=function(){var e={};this._uuid&&(e.uuid=this._uuid);this._userid&&(e.userid=this._userid);return e};return e}())),l=(c=null,function(){c||(c=new p);return c}),h=function(){var e=null;function t(){var e=!1,t=null,n=[];this.addPageStart=function(n){if(n&&!e){t={ts:Date.now(),path:n,page_name:n};e=!0}};this.addPageEnd=function(i){if(e&&i&&t&&i===t.page_name){var r=Date.now()-t.ts;t.duration=Math.abs(r);n.push(t);t=null;e=!1}};this.get=function(){return n};this.getCurrentPage=function(){return t};this.clear=function(){n.length=0}}return function(){e||(e=new t);return e}}(),d={};var g=function(){var e=null,n=[],i="";function r(){return{add:function(e,r){t().v("share origin: %o",e);var o={title:e&&e.title,path:e&&e.path&&e.path.split("?")[0],_um_sts:Date.now()};o.path&&o.path.length>1&&f.startsWith(o.path,"/")&&(o.path=f.trimStart(o.path,"/"));var s=e.path||"",a=l().getId();if(a){var u=i.split(","),c=(u=u.filter((function(e){return e.length>0}))).indexOf(a);c>=0&&(u=u.slice(0,c));u.length<3&&u.push(a);var p=u.join(",");-1!==s.indexOf("?")?s+="&_um_ssrc="+p:s+="?_um_ssrc="+p;var h=Date.now();s+="&_um_sts="+h;if(r){var g=function(e){var t=[];for(var n in e)"_um_ssrc"!==n&&"_um_sts"!==n&&t.push(n+"="+e[n]);return t.join("&")}(d),v=g?g+"&_um_ssrc="+p+"&_um_sts="+h:"_um_ssrc="+p+"&_um_sts="+h;e.query=e.query?e.query+"&_um_ssrc="+p+"&_um_sts="+h:v}else e.path=s;o._um_ssrc=p;o._um_sts=h}n.push(o);t().v("share: %o",e);return e},setShareSource:function(e){i=e},clear:function(){n.length=0},get:function(){return n}}}return function(){e||(e=new r);return e}}(),v=function(e){if(e)try{return JSON.stringify(e)}catch(e){}return""},_=function(e){if(e)try{return JSON.parse(e)}catch(e){}return null},y=function(){var e=null,t="",i=null,r=!1;function s(){this.load=function(e){if(i){o.removeStorage(t);e()}else{t="um_cache_"+n().appKey();o.getStorage(t,(function(n){i=_(n)||{};r=!0;o.removeStorage(t);e()}))}};this.save=function(){i&&o.setStorage(t,v(i))};this.set=function(e,t){i&&(i[e]=t)};this.get=function(e){return(i||{})[e]};this.remove=function(e){i&&i[e]&&delete i[e]};this.getAll=function(){return i};this.clear=function(){i=null};this.has=function(e){return!!this.get(e)};this.isLoaded=function(){return r}}return function(){e||(e=new s);return e}}(),m=function(){var e,n,i=[],r=[];function o(){if(i.length){var e=y().get("ekvs");if(function(e){var t=0;for(var n in e)Array.isArray(e[n])&&(t+=e[n].length);return t}(e)+i.length<=1e4){e=s(e,i);y().set("ekvs",e)}}}function s(e,t){var i=(e=e||{})[n];Array.isArray(i)&&i.length?e[n]=i.concat(t):e[n]=[].concat(t);return e}return function(){e||(e={addEvent:function(e){if(n){i.unshift(e);if(i.length>1){o();i.length=0}}else{t().w("session id is null: ",n);r.unshift(e)}},setSessionId:function(e){n=e;t().v("setSessionId: ",n);if(Array.isArray(r)&&r.length&&n){for(var i=0;i<r.length;i++)this.addEvent(r[i]);r.length=0}},getEkvs:function(){var e=y().get("ekvs");i&&i.length&&(e=s(e,i));return e},clear:function(){y().remove("ekvs");i.length=0}});return e}}(),S="2g",I="3g",O="4g",A="half_session",E="close_session",N="ekv",T=["access","access_subtype"],w=function(){var e=null;function t(){var e=!1,t={};function i(e){var i=y().get(u.IMPRINT);i&&(t.imprint=i);t.device_type="Phone";t.sdk_version=u.IMPL_VERSION;t.appkey=n().appKey();o.getDeviceInfo((function(e){t.device_info=e||""}));var r=o.getAppInfoSync();t.appid=r.appId;t.app_env=r.appEnv;t.app_version=r.appVersion;o.getSystemInfo((function(n){o.getNetworkInfo((function(i){var r=function(e,t){var n={};(e=e||{}).safeArea=e.safeArea||{};var i=(t=t||{}).networkType;"none"===i&&(i="unknown");var r=e.model||"",s=e.platform||"",a=e.brand||"",u=a.toLowerCase();n.sdk_type=o.getSdkType();n.platform=o.getPlatform();n.platform_sdk_version=e.platformSDKVersion;n.platform_version=e.platformVersion;n.resolution=e.resolution;n.pixel_ratio=e.pixelRatio;n.os=s;n.font_size_setting=e.fontSizeSetting;n.device_model=r;n.device_brand=a;n.device_manufacturer=u;n.device_manuid=r;n.device_name=r;n.os_version=e.OSVersion;n.language=e.language;n.theme=e.theme;n.benchmark_level=e.benchmarkLevel;n.status_bar_height=e.statusBarHeight;n.safe_area_top=e.safeArea.top;n.safe_area_left=e.safeArea.left;n.safe_area_right=e.safeArea.right;n.safe_area_bottom=e.safeArea.bottom;n.safe_area_height=e.safeArea.height;n.safe_area_width=e.safeArea.width;n.storage=e.storage;n.screen_width=e.screenWidth;n.screen_height=e.screenHeight;n.host=e.host;switch(i=i?i.toLowerCase():""){case O:n.access_subtype="LTE";n.access="4G";break;case I:n.access_subtype="CDMA";n.access="3G";break;case S:n.access_subtype="GRPS";n.access="2G";break;default:n.access=i;delete n.access_subtype}return n}(n,i);f.assign(t,r);e&&e()}))}))}return{init:function(){i((function(){e=!0}))},isLoaded:function(){return e},get:function(){return t},getRealtimeFields:function(){var e={};T.forEach((function(n){e[n]=t[n]}));return e},setIdTracking:function(e){this.setItem("id_tracking",e)},setIdType:function(e){this.setItem("id_type",e)},setAppVersion:function(e){this.setItem("app_version",e)},setSuperProperty:function(e){t.sp||(t.sp={});t.sp.isv=e},getSuperProperty:function(){return t&&t.sp?t.sp.isv:""},setItem:function(e,n){t[e]=n},getItem:function(e){return t[e]}}}return{instance:function(){e||(e=t());return e}}}(),k=function(){var e=null,n=null,i=null;function r(){return{resume:function(e){var r=!1;i||(i=y().get(u.CURRENT_SESSION));var s=new Date;n=s.getTime();if(!i||!i.end_time||n-i.end_time>u.SESSION_INTERVAL){r=!0;!function(e){try{var n=(i||{}).options||{},r=f.assign({},function(e){var n={};for(var i in e)0===i.indexOf("_um_")&&(n[i]=e[i]);t().v("query: ",e);t().v("_um_params: ",n);return n}(e.query));r.path=e.path||n.path;"gaode"!==o.getPlatform()&&(r.scene=e.scene?o.getPlatform()+"_"+e.scene:n.scene);var s=e.referrerInfo;s&&(r.referrerAppId=s.appId);t().v("session options: ",r);var a=r[u.UM_SSRC];a&&g().setShareSource(a);var c=Date.now();i={id:f.getRandomStr(10)+c,start_time:c,options:r}}catch(e){t().e("生成新session失败: ",e)}}(e);t().v("开始新的session(%s): ",i.id,i)}else t().v("延续上一次session(%s): %s ",i.id,s.toLocaleTimeString(),i);return r},pause:function(){!function(){if(i){var e=new Date;i.end_time=e.getTime();"number"!=typeof i.duration&&(i.duration=0);i.duration=i.end_time-n;y().set(u.CURRENT_SESSION,i);t().v("退出会话(%s): %s ",i.id,e.toLocaleTimeString(),i)}}()},getCurrentSessionId:function(){return(i||{}).id},getCurrentSession:function(){return i},cloneCurrentSession:function(){return f.clone(i)}}}return function(){e||(e=r());return e}}();function b(e){var t=null;switch(e){case A:t=function(){var e=null,t=k().cloneCurrentSession();t&&(e={header:{st:"1"},analytics:{sessions:[t]}});return e}();break;case E:t=function(){var e=null,t={},n=k().cloneCurrentSession();if(n){var i=h().get(),r=g().get();Array.isArray(i)&&i.length&&(n.pages=f.clone(i));Array.isArray(r)&&r.length&&(n.shares=f.clone(r));h().clear();g().clear();t.sessions=[n]}var o=m().getEkvs();if(o){t.ekvs=f.clone(o);m().clear()}(t.sessions||t.ekvs)&&(e={analytics:t});return e}();break;case N:t=function(){var e=null,t=m().getEkvs();if(t){e={analytics:{ekvs:f.clone(t)}};m().clear()}return e}()}return t}var D={sessions:"sn",ekvs:"e",active_user:"active_user"},U={sdk_type:"sdt",access:"ac",access_subtype:"acs",device_model:"dm",language:"lang",device_type:"dt",device_manufacturer:"dmf",device_name:"dn",platform_version:"pv",id_type:"it",font_size_setting:"fss",os_version:"ov",device_manuid:"did",platform_sdk_version:"psv",device_brand:"db",appkey:"ak",_id:"id",id_tracking:"itr",imprint:"imp",sdk_version:"sv",resolution:"rl",testToken:"ttn",theme:"t5",benchmark_level:"bml",screen_width:"sw",screen_height:"sh",status_bar_height:"sbh",safe_area_top:"sat",safe_area_left:"sal",safe_area_right:"sar",safe_area_bottom:"sab",safe_area_height:"sah",safe_area_width:"saw",pixel_ratio:"pr",storage:"s7",host:"hs"},P={uuid:"ud",unionid:"und",openid:"od",anonymousid:"nd",alipay_id:"ad",device_id:"dd",userid:"puid"};function R(e,t){var n=L(e,t);e&&e.id_tracking&&(n[t.id_tracking||"id_tracking"]=L(e.id_tracking,P));return n}function L(e,t){var n={};for(var i in e)t[i]?n[t[i]]=e[i]:n[i]=e[i];return n}function C(e,t){var n={};if(e)for(var i in e)e[i]&&(n[t[i]]=e[i]);return n}var M="";function x(){return M}var V="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",j=function(e){for(var t={},n=0,i=e.length;n<i;n++)t[e.charAt(n)]=n;return t}(V),F=String.fromCharCode,G=function(e){if(e.length<2){return(t=e.charCodeAt(0))<128?e:t<2048?F(192|t>>>6)+F(128|63&t):F(224|t>>>12&15)+F(128|t>>>6&63)+F(128|63&t)}var t=65536+1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320);return F(240|t>>>18&7)+F(128|t>>>12&63)+F(128|t>>>6&63)+F(128|63&t)},q=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,K=function(e){var t=[0,2,1][e.length%3],n=e.charCodeAt(0)<<16|(e.length>1?e.charCodeAt(1):0)<<8|(e.length>2?e.charCodeAt(2):0);return[V.charAt(n>>>18),V.charAt(n>>>12&63),t>=2?"=":V.charAt(n>>>6&63),t>=1?"=":V.charAt(63&n)].join("")},H=function(e){return t=function(e){return e.replace(q,G)}(e),t.replace(/[\s\S]{1,3}/g,K);var t},Y=new RegExp(["[À-ß][-¿]","[à-ï][-¿]{2}","[ð-÷][-¿]{3}"].join("|"),"g"),J=function(e){switch(e.length){case 4:var t=((7&e.charCodeAt(0))<<18|(63&e.charCodeAt(1))<<12|(63&e.charCodeAt(2))<<6|63&e.charCodeAt(3))-65536;return F(55296+(t>>>10))+F(56320+(1023&t));case 3:return F((15&e.charCodeAt(0))<<12|(63&e.charCodeAt(1))<<6|63&e.charCodeAt(2));default:return F((31&e.charCodeAt(0))<<6|63&e.charCodeAt(1))}},B=function(e){var t=e.length,n=t%4,i=(t>0?j[e.charAt(0)]<<18:0)|(t>1?j[e.charAt(1)]<<12:0)|(t>2?j[e.charAt(2)]<<6:0)|(t>3?j[e.charAt(3)]:0),r=[F(i>>>16),F(i>>>8&255),F(255&i)];r.length-=[0,0,2,1][n];return r.join("")},X=function(e){return t=function(e){return e.replace(/[\s\S]{1,4}/g,B)}(e),t.replace(Y,J);var t},W=function(e,t){return t?H(String(e)).replace(/[+\/]/g,(function(e){return"+"==e?"-":"_"})).replace(/=/g,""):H(String(e))},z=function(e){return X(String(e).replace(/[-_]/g,(function(e){return"-"==e?"+":"/"})).replace(/[^A-Za-z0-9\+\/]/g,""))};var Q=new function(){var e="",t=this;this.set=function(t){e=t};this.get=function(){return e};this.getImpObj=function(){return _(z(e))};this.getItem=function(e){var n=t.getImpObj();return n&&n[e]||""};this.load=function(){e=y().get(u.IMPRINT)};this.save=function(){e&&y().set(u.IMPRINT,e)}};function Z(e,n,i,r){w.instance().setIdType(l().getIdType());w.instance().setIdTracking(l().getIdTracking());var s=l().getUserId();s&&e.analytics&&(e.analytics.active_user={puid:s,provider:l().getProvider()});var a=f.clone(w.instance().get());e.header=f.assign(a,e.header,{ts:Date.now(),testToken:x(),traceId:f.getRandomStr(10)+Date.now()+f.getRandomStr(9)});var c=function(e){return{h:R(e.header,U),a:C(e.analytics,D)}}(e),p=v(c),h={url:u.ENDPOINT+u.LOG_URL,method:"POST",data:c,success:function(r){var o=r.code||r.status||r.statusCode;if(200===o||413===o){t().i("数据发送成功: ",e,p);!function(e){if(e){w.instance().setItem(u.IMPRINT,e);Q.set(e);Q.save();t().v("imprint: ",Q.getImpObj());Q.getItem("ttn_invalid")&&(M="")}}((r.data||{}).imprint);"function"==typeof n&&n(r)}else{t().w("数据发送失败: ",p);"function"==typeof i&&i()}},fail:function(e){t().w("超时: ",p);"function"==typeof i&&i()},complete:function(){"function"==typeof r&&r()}};o.request(f.assign(h,{header:{"Msg-Type":o.getSdkType()+"/json","disable-base64":"Y"}}))}function $(e){var t=e,n=[];this.enqueue=function(e){"number"==typeof t&&this.size()>=t&&this.dequeue();n.push(e)};this.dequeue=function(){return n.shift()};this.front=function(){return n[0]};this.isEmpty=function(){return 0===n.length};this.clear=function(){n.length=0};this.size=function(){return n.length};this.items=function(){return n};this.print=function(){console.log(n.toString())}}var ee=function(){var e=null,n=!1,i=[],r=new $(50);function o(e,t,n){if(w.instance().isLoaded()){t=t||{};var i=b(e);if(i){var s=w.instance().getRealtimeFields();i.header=f.assign({},i.header,s);i.noCache=t.noCache;r.enqueue(i)}"function"==typeof n&&n()}else setTimeout((function(){o(e,t,n)}),100)}function s(e){var t=r.front();if(t)Z(t,(function(){r.dequeue();s(e)}),(function(){var t=r.dequeue();t&&!t.noCache&&i.push(t);s(e)}));else{!function(){i.forEach((function(e){r.enqueue(e)}));i.length=0}();e()}}function a(e){if(l().getId())if(n)t().i("队列正在发送中");else{n=!0;s((function(){n=!1;"function"==typeof e&&e()}))}else{t().i("获取id标识失败，暂缓发送");"function"==typeof e&&e()}}function c(){this.send=function(e,t,n){e?this.add(e,t,(function(){a(n)})):a(n)};this.add=function(e,t,n){o(e,t,n)};this.load=function(){var e=y().get(u.REQUESTS);e&&e.length&&e.forEach((function(e){r.enqueue(e)}));y().remove(u.REQUESTS)};this.save=function(){y().set(u.REQUESTS,f.clone(r.items()));r.clear()}}return function(){e||(e=new c);return e}}(),te=function(){var e=null,i=null;function r(){this.setUserInfo=function(e){i=e};this.update=function(){e(i)||o.getUserInfo((function(t){e(t)}))};function e(e){if(e&&"object"==typeof e){var i=y().get(u.USER_INFO);i&&f.deepEqual(e,i)||function(e,i){var r=n().appKey(),s=o.getSdkType(),a=l().getId(),c=l().getIdType();if(!(r&&s&&a&&c))return;var f={ak:n().appKey(),sdt:o.getSdkType(),uin:e.nickName,uia:e.avatar||e.avatarUrl,uig:e.gender,uit:e.country,uip:e.province,uic:e.city,uil:e.language,id:l().getId(),it:l().getIdType(),age:e.age,cln:e.constellation},p=JSON.stringify(f);p=W(p);o.request({url:u.ENDPOINT+u.USERINFO_URL,method:"POST",header:{"content-type":"application/x-www-form-urlencoded"},data:"ui="+p,success:function(n){t().v("用户信息上传成功: ",e);i&&i(n&&n.data&&200===n.data.code)},fail:function(){t().e("用户信息上传失败: ",e);i&&i(!1)}})}(e,(function(t){t&&y().set(u.USER_INFO,e)}));return!0}return!1}}return function(){e||(e=new r);return e}}();function ne(e,t){this.id=e;this.ts=Date.now();var n=typeof t;if("string"===n&&t)this[e]=t;else if("object"===n)for(var i in t)({}).hasOwnProperty.call(t,i)&&(this[i]=t[i])}function ie(){var e=!1,i=!1,r=0;this.init=function(i){t().v("sdk version: "+u.IMPL_VERSION);e?t().v("Lib重复实例化"):y().load((function(){t().v("cache初始化成功: ",y().getAll());!function(){l().setUseOpenid&&l().setUseOpenid(n().useOpenid());l().init((function(){w.instance().init();t().v("Header初始化成功")}))}();e=!0;"function"==typeof i&&i();t().tip("SDK集成成功")}))};this.resume=function(r){if(e&&!i){t().v("showOptions: ",r);var o;i=!0;n().enableVerify()&&r&&r.query&&(o=r.query._ttn,M=o||M);this._resume(r)}};this._resume=function(e){ee().load();var i=k().resume(e),r=k().getCurrentSessionId();m().setSessionId(r);i&&ee().add(A,{},(function(){l().setUseOpenid&&l().setUseOpenid(n().useOpenid());if(n().useOpenid()&&n().autoGetOpenid()&&!l().getId()){t().v("get id async");o(10,3e3)}else{t().v("session auto send");ee().send()}}));function o(e,i){l().getId()||e<=0||l().getOpenIdAsync(n().appKey(),(function(n){if(n){t().v("获取id成功");ee().send()}else{t().v("获取openid失败,启动重试,剩余可用次数",e-1);setTimeout((function(){o(e-1,i)}),i)}}))}};this.pause=function(o){if(e){i=!1;r=0;k().pause();n().uploadUserInfo()&&te().update();ee().send(E,{},(function(){ee().save();y().save();t().v("cache save success");"function"==typeof o&&o()}))}};this.setOpenid=function(e){t().v("setOpenId: %s",e);l().setOpenid(e);ee().send()};this.setUnionid=function(e){t().v("setUnionid: %s",e);l().setUnionid(e)};this.setUserid=function(e,n){t().v("setUserid: %s",e,n);l().setUserid(e,n)};this.removeUserid=function(){t().v("removeUserid");l().removeUserid()};this.setUserInfo=function(e){t().v("setUserInfo: %s",e);te().setUserInfo(e)};this.setAnonymousid=function(e){t().v("setAnonymousId: %s",e);l().setAnonymousid(e);ee().send()};this.setAppVersion=function(e){e&&"string"!=typeof e?t().w("setAppVersion方法只接受字符串类型参数"):w.instance().setAppVersion(e)};this.setAlipayUserid=function(e){if(e&&"string"!=typeof e)t().w("setAlipayUserid方法只接受字符串类型参数");else{t().v("setAlipayUserid: %s",e);l().setAlipayUserid(e)}};this.setDeviceId=function(e){if("string"==typeof e){l().setDeviceId(e);return e}};this.setSuperProperty=function(e){if(e&&"string"!=typeof e)t().w("超级属性只支持字符串类型");else{var n=this;if(w.instance().getSuperProperty()!==e){w.instance().setSuperProperty(e);n.pause((function(){n.resume()}))}}};this.trackEvent=function(n,i){if(e){t().v("event: ",n,i);if(function(e,n){if(!e||"string"!=typeof e){t().e('please check trackEvent id. id should be "string" and not null');return!1}var i=["id","ts","du"],r={};i.forEach((function(e){r[e]=1}));if(r[e]){t().e("eventId不能与以下保留字冲突: "+i.join(","));return!1}if(e.length>u.MAX_EVENTID_LENGTH){t().e("The maximum length of event id shall not exceed "+u.MAX_EVENTID_LENGTH);return!1}if(n&&("object"!=typeof n||Array.isArray(n))&&"string"!=typeof n){t().e("please check trackEvent properties. properties should be string or object(not include Array)");return!1}if("object"==typeof n){var o=0;for(var s in n)if({}.hasOwnProperty.call(n,s)){if(s.length>u.MAX_PROPERTY_KEY_LENGTH){t().e("The maximum length of property key shall not exceed "+u.MAX_PROPERTY_KEY_LENGTH);return!1}if(o>=u.MAX_PROPERTY_KEYS_COUNT){t().e("The maximum count of properties shall not exceed "+u.MAX_PROPERTY_KEYS_COUNT);return!1}if(r[s]){t().e("属性中的key不能与以下保留字冲突: "+i.join(","));return!1}o+=1}}return!0}(n,i)){var o=new ne(n,i);m().addEvent(o);var s=!!x(),a=s?0:u.EVENT_SEND_DEFAULT_INTERVAL,c=Date.now();if(function(e,t){return"number"!=typeof r||"number"!=typeof t||r<=0||e-r>t}(c,a)){r=c;ee().send(N,{noCache:s},(function(){}))}}}};this.trackShare=function(n){if(e)try{if(o.getSdkType().indexOf("game")>-1){n=g().add(n,!0);t().v("shareQuery: ",n)}else{n=g().add(n,!1);t().v("sharePath: ",n.path)}}catch(e){t().v("shareAppMessage: ",e)}return n};this.trackPageStart=function(t){e&&h().addPageStart(t)};this.trackPageEnd=function(t){e&&h().addPageEnd(t)};this.onShareAppMessage=function(e){var t=this;o.onShareAppMessage((function(){return t.trackShare(e())}))};this.shareAppMessage=function(e){this.trackShare(e);o.shareAppMessage(e)}}var re=[];function oe(){}oe.prototype={createMethod:function(e,n,i){try{e[n]=i&&i[n]?function(){return i[n].apply(i,arguments)}:function(){re.push([n,[].slice.call(arguments)])}}catch(e){t().v("create method errror: ",e)}},installApi:function(e,n){try{var i,r,o="resume,pause,trackEvent,trackPageStart,trackPageEnd,trackShare,setUserid,setOpenid,setUnionid,setSuperProperty,setUserInfo".split(",");for(i=0,r=o.length;i<r;i++)this.createMethod(e,o[i],n);if(n)for(i=0,r=re.length;i<r;i++){var s=re[i];try{n[s[0]].apply(n,s[1])}catch(e){t().v("impl[v[0]].apply error: ",s[0],e)}}}catch(e){t().v("install api errror: ",e)}}};var se=[u.ENDPOINT,u.ENDPOINTB];function ae(e,n){var i,r;0===e||1===e&&n?i=u.ENDPOINT:2===e&&n?i=u.ENDPOINTB:n&&(i=se[e]);if(e>=se.length||n){n&&(r=i,u.ENDPOINT=r);n&&t().v("命中可用服务",i);!n&&t().tip_w("未命中可用服务");return!1}o.request({url:u.ENDPOINT+"/uminiprogram_logs/ckdh",success:function(t){200===(t.code||t.status||t.statusCode)&&t.data&&200===t.data.code?ae(e+1,!0):ae(e+1,!1)},fail:function(){ae(e+1,!1)}})}var ue={init:function(e){u.ENDPOINTB&&setTimeout((function(){ae(0,!1)}),e)}},ce=new oe,fe={_inited:!1,_log:t(),preinit:function(e){if(e&&"object"==typeof e)for(var t in e)u[t]=e[t];return u},use:function(e,t){e&&f.isFunction(e.install)?e.install(fe,t):f.isFunction(e)&&e(fe,t);return fe},messager:r,init:function(e){if(this._inited)t().v("已经实例过，请避免重复初始化");else if(e)if(e.appKey){"boolean"!=typeof e.useOpenid&&(e.useOpenid=!0);n().set(e);t().setDebug(e.debug);this._inited=!0;var i=this;r.emit(r.messageType.CONFIG_LOADED,e);try{var o=new ie;t().v("成功创建Lib对象");0;o.init((function(){t().v("Lib对象初始化成功");ce.installApi(i,o);t().v("安装Lib接口成功");r.emit(r.messageType.UMA_LIB_INITED,e)}));ue.init(3e3)}catch(e){t().w("创建Lib对象异常: "+e)}}else t().err("请确保传入正确的appkey");else t().err("请正确设置相关信息！")}};try{ce.installApi(fe,null)}catch(e){t().w("uma赋值异常: ",e)}var pe="https://ucc.umeng.com/v1/mini/fetch",le="https://pslog.umeng.com/mini_ablog",he="2.8.0",de="none",ge={},ve=Array.isArray;ge.isArray=ve||function(e){return"[object Array]"===toString.call(e)};ge.isObject=function(e){return e===Object(e)&&!ge.isArray(e)};ge.isEmptyObject=function(e){if(ge.isObject(e)){for(var t in e)if(hasOwnProperty.call(e,t))return!1;return!0}return!1};ge.isUndefined=function(e){return void 0===e};ge.isString=function(e){return"[object String]"===toString.call(e)};ge.isDate=function(e){return"[object Date]"===toString.call(e)};ge.isNumber=function(e){return"[object Number]"===toString.call(e)};ge.each=function(e,t,n){if(null!=e){var i={},r=Array.prototype.forEach;if(r&&e.forEach===r)e.forEach(t,n);else if(e.length===+e.length){for(var o=0,s=e.length;o<s;o++)if(o in e&&t.call(n,e[o],o,e)===i)return}else for(var a in e)if(hasOwnProperty.call(e,a)&&t.call(n,e[a],a,e)===i)return}};ge.buildQuery=function(e,t){var n,i,r=[];void 0===t&&(t="&");ge.each(e,(function(e,t){n=encodeURIComponent(e.toString());i=encodeURIComponent(t);r[r.length]=i+"="+n}));return r.join(t)};ge.JSONDecode=function(e){if(e){try{return JSON.parse(e)}catch(e){console.error("JSONDecode error",e)}return null}};ge.JSONEncode=function(e){try{return JSON.stringify(e)}catch(e){console.error("JSONEncode error",e)}};var _e=Object.create(null);function ye(e){t().v("开始构建 fetch body");o.getSystemInfo((function(t){o.getNetworkInfo((function(i){var r=(i=i||{}).networkType;r=r===de?"unknown":r.toUpperCase();_e.access=r;!function(e,t){var i=e.brand||"";_e.deviceType="Phone";_e.sdkVersion=he;_e.appkey=n().appKey();_e.sdkType=o.getSdkType();_e.umid=l().getId();if(e){_e.language=e.language||"";_e.os=e.OS;_e.osVersion=e.OSVersion;_e.deviceName=e.deviceName;_e.platformVersion=e.platformVersion;_e.platformSdkVersion=e.platformSDKVersion;_e.deviceBrand=i;var r=e.resolution.split("*");if(ge.isArray(r)){_e.resolutionHeight=Number(r[0]);_e.resolutionWidth=Number(r[1])}}!function(e){if(e){_e.installTime=e.install_datetime&&Date.parse(e.install_datetime);_e.scene=e.install_scene;_e.channel=e.install_channel;_e.campaign=e.install_campaign}}(Q.getImpObj());t&&t(_e)}(t,e)}))}))}var me=Object.create(null),Se=null,Ie=!1,Oe={minFetchIntervalSeconds:43200};function Ae(e){e&&ge.each(e,(function(e){me[e.k]=e}))}function Ee(){var e=this;this.STORAGE_NAME=null;r.once(r.messageType.CONFIG_LOADED,(function(n){t().v("云配初始化开始...");e.init(n)}))}Ee.prototype={setDefaultValues:function(e){Ie&&ge.isObject(e)&&ge.each(e,(function(e,t){me[t]&&me[t].v||(me[t]={v:e})}))},getValue:function(e){t().v("从配置项中读取 value, 当前配置为: ",me);t().v("待读取的 key : ",e);try{if(!Ie)return;var i=me[e]||{};t().v("读取相应配置ing..., 结果为: ",i);if(ge.isNumber(i.e)&&ge.isNumber(i.g)){t().v("读取到相应配置, 开始数据上报...");!function(e){var i={appkey:n().appKey(),sdkType:o.getSdkType(),expId:e&&e.e,groupId:e&&e.g,clientTs:Date.now(),key:e&&e.k,value:e&&e.v,umid:l().getId()};try{o.request({url:le,method:"POST",data:[i],success:function(e){e&&200===e.statusCode?t().v("上传数据成功",i):t().w("ablog 请求成功, 返回结果异常 ",e)},fail:function(e){t().w("ablog 请求数据错误 ",i,e)}})}catch(e){t().w("urequest 调用错误",e)}}(i)}return i.v}catch(n){t().w("getValue error, key: ",e)}},active:function(e){try{if(!Ie)return;var n,i;e&&e.params&&(n=e.params);e&&e.callback&&(i=e.callback);t().v("激活配置项: ",n);if(n){t().v("本地已缓存的配置项: ",me);Ae(n);t().v("合并后的配置项: ",me);i&&i(me);t().v("active 结束")}else{t().v("配置项为空!! 读取本地配置...");o.getStorage(this.STORAGE_NAME,(function(e){if(e){Ae((e=ge.JSONDecode(e)||{}).params);t().v("当前本地配置项为: ",me);i&&i(me);t().v("active 结束")}else t().v("当前本地配置项为空, 退出激活")}))}}catch(e){t().w("SDK active 错误",e)}},init:function(e){if(e.appKey){Se=e.appKey;this.STORAGE_NAME="um_remote_config_{{"+Se+"}}"}if(Se)if(Ie)t().w("SDK 已经初始化, 请避免重复初始化");else{Ie=!0;this.setOptions(e);this.active()}else t().err("请检查您的小程序 appKey, appKey 不能为空")},setOptions:function(e){if(ge.isObject(e)){var t=e.minFetchIntervalSeconds;ge.isNumber(t)&&(Oe.minFetchIntervalSeconds=Math.max(t,5))}},fetch:function(e){if(Ie&&this.STORAGE_NAME){var n,i;e&&e.active&&(n=e.active);e&&e.callback&&(i=e.callback);var r=this;o.getStorage(this.STORAGE_NAME,(function(e){t().v("开始读缓存 data is ",e);if((e=ge.JSONDecode(e)||{}).params&&e.ts&&Date.now()-e.ts<1e3*Oe.minFetchIntervalSeconds){t().v("缓存数据存在, 并且本次触发时间距离上次fetch触发时间未超过 fetch 时间间隔, 无需 fetch");i&&i(e.params)}else ye((function(e){t().v("缓存数据不存在, 构建 fetch body :",e);try{o.request({url:pe,method:"POST",data:e,success:function(e){if(e&&200===e.statusCode&&e.data&&e.data.cc){t().v("fetch 请求成功, 响应数据: ",e.data);var s=Object.create(null);ge.each(e.data.cc,(function(e){s[e.k]=e}));var a={ts:Date.now(),params:s};t().v("开始缓存 fetch 请求的云配置结果...");o.setStorage(r.STORAGE_NAME,ge.JSONEncode(a),(function(e){t().v("缓存云配置成功, 缓存数据为: ",a);t().v("缓存云配置成功, 成功消息为: ",e);t().v("云配拉取数据是否自动激活: ",n);if(e&&n){t().v("激活云配置...");r.active({params:s,callback:i})}}))}else{t().w("fetch 请求成功,返回结果异常 ",e.data);i&&i()}},fail:function(n){t().w("fetch请求数据错误 ",e,n);i&&i()}})}catch(e){t().w("urequest调用错误",e)}}))}))}}};var Ne={install:function(e,t){e.rc||(e.rc=new Ee);e.messager.once(e.messager.messageType.CONFIG_LOADED,(function(){e._log.v("plugin rc installed")}));return e.rc}},Te=!1,we={install:function(e,t){e.wxpluginwraper||(e.wxpluginwraper=function(t){if(!Te){t.onAppShow&&t.onAppShow((function(t){e.resume(t)}));t.onAppHide&&t.onAppHide((function(t){e.pause(t)}));Te=!0}})}},ke="",be={};function De(e){e&&(ke=e)}function Ue(e,t){if(e.onShareAppMessage){var n=e.onShareAppMessage;e.onShareAppMessage=function(e){var i=n.call(this,e)||{},r=function(e,t){if(!e)return"";var n=[];for(var i in t)"_um_ssrc"!==i&&"_um_sts"!==i&&n.push(i+"="+t[i]);var r=n.join("&");return r?e+"?"+r:e}(ke,be[ke]);!i.path&&r&&(i.path=r);var o=t.trackShare.call(this,i);return void 0===o?i:o}}}function Pe(e,t,n){var i=e[t];e[t]=function(e){n.call(this,e);i&&i.call(this,e)}}function Re(e){try{fe.resume(e,!0)}catch(e){t().v("onAppShow: ",e)}}function Le(){try{fe.pause()}catch(e){t().v("onAppHide: ",e)}}function Ce(){try{De(this.route);fe.trackPageStart(this.route)}catch(e){t().v("onPageShow: ",e)}}function Me(e){try{De(this.route);e&&(n=this.route,i=e,n&&(be[n]=i));t().v("Page onLoad: ",this.route,e)}catch(e){t().v("onPageLoad: ",e)}var n,i}function xe(){try{fe.trackPageEnd(this.route)}catch(e){t().v("onPageHide: ",e)}}try{var Ve=App;App=function(e){Pe(e,"onLaunch",(function(){!function(e){try{fe.init(e)}catch(e){t().v("onAppLaunch: ",e)}}(e.umengConfig)}));Pe(e,"onShow",Re);Pe(e,"onHide",Le);Ve(e)}}catch(e){t().w("App重写异常")}try{var je=Page;Page=function(e){Pe(e,"onShow",Ce);Pe(e,"onHide",xe);Pe(e,"onUnload",xe);Pe(e,"onLoad",Me);Ue(e,fe);je(e)}}catch(e){t().w("Page重写异常")}try{var Fe=Component;Component=function(e){try{e.methods=e.methods||{};var t=e.methods;Pe(t,"onShow",Ce);Pe(t,"onHide",xe);Pe(t,"onUnload",xe);Pe(t,"onLoad",Me);Ue(t,fe);Fe(e)}catch(t){Fe(e)}}}catch(e){t().w("Component重写异常")}var Ge=fe.init;fe.init=function(e){if(e&&e.useOpenid){t().tip_w(t().repeat("!"));t().tip_w("openid已开启，请确保使用setOpenid设置openid或通过设置autoGetOpenid为true，并在友盟后台设置secret由友盟帮您获取");t().tip_w(t().repeat("!"))}Ge.call(fe,e)};fe.use(Ne);fe.use(we);wx.uma=fe;module.exports=fe;
