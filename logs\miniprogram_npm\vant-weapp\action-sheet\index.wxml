<wxs src="../wxs/utils.wxs" module="utils" />

<van-popup
  show="{{ show }}"
  position="bottom"
  z-index="{{ zIndex }}"
  overlay="{{ overlay }}"
  custom-style="{{ customStyle }}"
  overlay-style="{{ overlayStyle }}"
  custom-class="van-action-sheet"
  safe-area-inset-bottom="{{ safeAreaInsetBottom }}"
  close-on-click-overlay="{{ closeOnClickOverlay }}"
  bind:close="onClose"
>
  <view wx:if="{{ title }}" class="van-hairline--bottom van-action-sheet__header">
    {{ title }}
    <van-icon
      name="close"
      custom-class="van-action-sheet__close"
      bind:click="onClose"
    />
  </view>
  <view wx:if="{{ actions && actions.length }}">
    <!-- button外包一层view，防止actions动态变化，导致渲染时button被打散 -->
    <button
      wx:for="{{ actions }}"
      wx:key="index"
      open-type="{{ item.openType }}"
      class="{{ utils.bem('action-sheet__item', { disabled: item.disabled || item.loading }) }} van-hairline--top {{ item.className || '' }}"
      hover-class="van-action-sheet__item--hover"
      data-index="{{ index }}"
      bind:tap="onSelect"
    >
      <block wx:if="{{ !item.loading }}">
        {{ item.name }}
        <text wx:if="{{ item.subname }}" class="van-action-sheet__subname" >{{ item.subname }}</text>
      </block>
      <van-loading wx:else size="20px" />
    </button>
  </view>
  <slot />
  <view
    wx:if="{{ cancelText }}"
    class="van-action-sheet__cancel"
    hover-class="van-action-sheet__cancel--hover"
    hover-stay-time="70"
    bind:tap="onCancel"
  >
    {{ cancelText }}
  </view>
</van-popup>
