<!--index.wxml-->
<scroll-view class="scrollarea" scroll-y type="list">
  <view class="container">
    <!-- 顶部社区信息 -->
    <view class="community-header">
      <image class="community-banner" src="{{communityInfo.communityBanner}}" mode="aspectFill"></image>
      <view class="community-info">
        <view class="community-name">{{communityInfo.communityName}}</view>
        <view class="weather-info" wx:if="{{weather}}">
          <text>{{weather.temp}}°C</text>
          <text>{{weather.text}}</text>
        </view>
      </view>
    </view>

    <!-- 用户信息卡片 -->
    <view class="user-card-container" wx:if="{{isLogin}}">
      <van-card
        thumb="{{userInfo.avatarUrl}}"
        title="{{ownerInfo.ownerName}}"
        desc="{{ownerInfo.houseStr}}"
        custom-class="user-card"
        thumb-class="user-avatar"
      >
        <view slot="tags" class="user-tags">
          <van-tag
            type="{{ownerInfo.role == '1' ? 'success' : ownerInfo.role == '2' ? 'primary' : 'warning'}}"
            size="medium"
            round
          >
            <block wx:if="{{ownerInfo.role == '1'}}">业主</block>
            <block wx:elif="{{ownerInfo.role == '2'}}">家庭成员</block>
            <block wx:elif="{{ownerInfo.role == '3'}}">租户</block>
            <block wx:else>业主</block>
          </van-tag>
          <text class="phone-text" wx:if="{{ownerInfo.mobile}}">{{ownerInfo.mobile}}</text>
        </view>
      </van-card>
    </view>


    <!-- 功能模块 -->
    <view class="function-grid-container">
      <van-row gutter="20" custom-class="function-grid">
        <van-col span="8">
          <view class="grid-item" bindtap="goToRepair">
            <van-icon name="setting-o" size="56rpx" color="#07c160" />
            <text>物业报修</text>
          </view>
        </van-col>
        <van-col span="8">
          <view class="grid-item" bindtap="goToPayment">
            <van-icon name="gold-coin-o" size="56rpx" color="#07c160" />
            <text>物业缴费</text>
          </view>
        </van-col>
        <van-col span="8">
          <view class="grid-item" bindtap="goToVisitor">
            <van-icon name="friends-o" size="56rpx" color="#07c160" />
            <text>邀请住户</text>
          </view>
        </van-col>
        <van-col span="8">
          <view class="grid-item" bindtap="goToOcInfo">
            <van-icon name="home-o" size="56rpx" color="#07c160" />
            <text>小区信息</text>
          </view>
        </van-col>
        <van-col span="8">
          <view class="grid-item" bindtap="goToServicePhone">
            <van-icon name="phone-o" size="56rpx" color="#07c160" />
            <text>服务电话</text>
          </view>
        </van-col>
        <van-col span="8">
          <view class="grid-item" bindtap="goToComplaint">
            <van-icon name="chat-o" size="56rpx" color="#07c160" />
            <text>投诉建议</text>
          </view>
        </van-col>
      </van-row>
    </view>

    <!-- 社区动态 -->
    <view class="community-news-container">
      <view class="community-news">
        <view class="section-title">
          <text>公告动态</text>
          <text class="more" bindtap="{{isLogin ? 'goToNoticeList' : 'goToLogin'}}">更多</text>
        </view>

        <van-cell-group custom-class="news-list">
          <van-cell
            wx:for="{{newsList}}"
            wx:key="id"
            title="{{item.title}}"
            label="{{item.time}}"
            is-link
            bindtap="{{isLogin ? 'goToNewsDetail' : 'goToLogin'}}"
            data-id="{{item.id}}"
            custom-class="news-item"
          >
            <view slot="icon" class="news-icon">
              <van-icon
                name="{{index === 0 ? 'volume-o' : index === 1 ? 'bell' : 'newspaper-o'}}"
                size="44rpx"
                color="{{index === 0 ? '#07c160' : index === 1 ? '#ff9500' : '#576b95'}}"
              />
            </view>
            <view slot="right-icon" class="news-tag">
              <van-tag
                type="{{item.type === 'notice' ? 'warning' : 'success'}}"
                size="small"
                round
              >
                {{item.type === 'notice' ? '通知' : '公告'}}
              </van-tag>
            </view>
          </van-cell>
        </van-cell-group>
      </view>
    </view>
  </view>
</scroll-view>
