<wxs src="../wxs/utils.wxs" module="utils" />

<view class="van-submit-bar custom-class">
  <slot name="top" />

  <view class="van-submit-bar__tip">
    <van-icon
      wx:if="{{ tipIcon }}"
      size="12px"
      name="{{ tipIcon }}"
      custom-class="van-submit-bar__tip-icon"
    />
    <view wx:if="{{ hasTip }}" class="van-submit-bar__tip-text">
      {{ tip }}
    </view>
    <slot name="tip" />
  </view>

  <view class="bar-class {{ utils.bem('submit-bar__bar', { safe: safeAreaInsetBottom && isIPhoneX }) }}">
    <slot />
    <view wx:if="{{ hasPrice }}" class="van-submit-bar__text">
      <text>{{ label || '合计：' }}</text>
      <text class="van-submit-bar__price price-class">
        <text class="van-submit-bar__currency">{{ currency }} </text>
        <text>{{ priceStr }}</text>
      </text>
      <text class="van-submit-bar__suffix-label">{{ suffixLabel }}</text>
    </view>
    <van-button
      square
      size="large"
      type="{{ buttonType }}"
      loading="{{ loading }}"
      disabled="{{ disabled }}"
      class="van-submit-bar__button"
      custom-class="button-class"
      bind:click="onSubmit"
    >
      {{ loading ? '' : buttonText }}
    </van-button>
  </view>
</view>
