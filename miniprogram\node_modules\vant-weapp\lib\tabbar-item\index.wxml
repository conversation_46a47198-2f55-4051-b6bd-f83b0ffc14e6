<wxs src="../wxs/utils.wxs" module="utils" />

<view
  class="{{ utils.bem('tabbar-item', { active }) }} custom-class"
  style="color: {{ active ? activeColor : inactiveColor }}"
  bind:tap="onClick"
>
  <view class="{{ utils.bem('tabbar-item__icon', { dot }) }}">
    <van-icon
      wx:if="{{ icon }}"
      name="{{ icon }}"
      customStyle="display: block"
    />
    <block wx:else>
      <slot
        wx:if="{{ active }}"
        name="icon-active"
      />
      <slot wx:else name="icon" />
    </block>
    <van-info
      wx:if="{{ info !== null }}"
      info="{{ info }}"
      custom-style="margin-top: 2px"
    />
  </view>
  <view class="van-tabbar-item__text">
    <slot />
  </view>
</view>
